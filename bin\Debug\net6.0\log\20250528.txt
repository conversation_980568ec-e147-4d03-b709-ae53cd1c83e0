2025-05-28 10:41:45,801 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (65ms) [Parameters=[@__model_Account_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-05-28 10:41:45,911 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-05-28 10:41:46,655 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:41:46,659 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:41:46,683 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:41:51,702 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:41:57,108 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:41:57,109 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:45:56,614 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__model_Account_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-05-28 10:45:56,614 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (127ms) [Parameters=[@__model_Account_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-05-28 10:45:56,763 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-05-28 10:45:56,763 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-05-28 10:45:57,141 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:45:57,141 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:45:57,148 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:45:57,878 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:45:57,929 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:45:57,929 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:53:25,257 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (76ms) [Parameters=[@__model_Account_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-05-28 10:53:25,308 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__model_Account_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-05-28 10:53:25,394 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-05-28 10:53:25,394 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-05-28 10:53:26,256 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:53:26,256 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:53:26,277 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:53:30,760 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:53:30,761 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:54:27,926 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:54:27,928 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:54:27,926 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:54:28,410 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:54:28,410 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:55:11,520 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:55:11,520 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:55:11,522 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:55:11,997 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:55:11,998 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:55:13,391 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:55:13,392 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:55:13,392 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:55:13,898 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:55:13,898 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:55:16,134 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:55:16,134 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:55:16,135 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:55:16,376 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:55:16,376 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:58:20,547 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:58:20,547 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:58:20,548 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:58:21,036 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 10:58:21,036 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:00:15,217 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (4ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:00:15,217 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:00:15,222 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (4ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:00:15,861 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:00:15,861 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:00:41,854 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:00:41,854 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:00:41,854 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:00:42,361 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:00:42,361 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:01:16,151 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:01:16,151 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:01:16,151 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:01:16,883 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:01:16,883 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:02:02,241 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:02:02,242 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:02:02,244 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:02:02,802 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:02:02,802 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:02:18,931 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:02:18,933 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:02:18,932 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:02:19,463 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:02:19,463 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:02:38,511 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:02:38,512 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:02:38,513 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:02:38,914 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:02:38,922 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:02:43,953 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:02:43,953 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:02:43,956 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:02:44,379 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:02:44,379 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:03:24,928 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:03:24,929 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:03:24,929 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:03:25,467 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:03:25,467 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:03:46,801 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:03:46,801 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:03:46,801 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:03:47,478 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:03:47,478 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:04:02,928 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:04:02,928 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:04:02,929 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:04:03,378 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:04:03,379 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:07:31,346 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:07:31,346 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:07:31,346 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:07:31,991 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:07:31,992 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:09:21,020 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:30:55,648 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (94ms) [Parameters=[@__model_Account_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-05-28 11:30:55,837 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-05-28 11:30:56,307 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:30:56,316 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:30:56,319 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:31:24,484 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:31:24,499 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (7ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:31:32,260 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__model_Account_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-05-28 11:31:32,265 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-05-28 11:31:32,531 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:31:32,531 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:31:32,541 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:33:28,734 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:33:28,734 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:33:28,740 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:33:31,743 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:33:31,745 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:33:31,757 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:33:42,141 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__model_Account_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-05-28 11:33:42,149 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-05-28 11:33:42,572 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:33:42,572 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 11:33:42,574 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:03:41,796 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (81ms) [Parameters=[@__model_Account_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-05-28 13:03:41,949 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-05-28 13:03:42,376 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:03:42,386 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:03:42,387 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:04:09,476 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:04:09,476 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:04:09,481 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:04:14,199 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:04:14,199 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:04:14,200 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:04:33,362 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:04:33,362 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:04:33,362 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:04:36,173 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:04:36,173 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:04:36,178 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:14:50,196 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (83ms) [Parameters=[@__model_Account_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-05-28 13:14:50,197 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (83ms) [Parameters=[@__model_Account_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-05-28 13:14:50,347 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-05-28 13:14:50,347 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-05-28 13:14:50,836 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:14:50,836 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:14:50,843 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:15:31,004 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:15:31,004 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:15:31,004 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:15:35,068 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:15:35,068 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:15:35,068 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:16:38,855 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__model_Account_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-05-28 13:16:38,866 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-05-28 13:16:39,244 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:16:39,244 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:16:39,245 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:17:12,485 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:17:12,493 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:17:12,493 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:17:14,896 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:17:16,992 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:17:16,992 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:17:30,426 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:18:16,823 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (7ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 13:18:16,823 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (7ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:16:05,046 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (14ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 14:16:05,540 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (4ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:16:05,541 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:16:05,591 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:16:09,810 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:16:09,812 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:16:15,198 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:16:15,199 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:16:19,457 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:22:22,354 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:22:22,356 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:22:22,360 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:22:23,628 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:22:23,732 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:22:23,742 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:35:34,660 [126] ERROR Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware [(null)] - An unhandled exception has occurred while executing the request.
<log4net.Error>Exception rendering object type [Microsoft.CSharp.RuntimeBinder.RuntimeBinderException]<stackTrace>System.TypeLoadException: Could not load type 'AspNetCoreGeneratedDocument.Views_Home_Index+<ExecuteAsync>d__9#1' from assembly 'OpenAuth.Customer.Mvc, Version=*******, Culture=neutral, PublicKeyToken=null'.
   at System.Reflection.CustomAttribute._CreateCaObject(RuntimeModule pModule, RuntimeType type, IRuntimeMethodInfo pCtor, Byte** ppBlob, Byte* pEndBlob, Int32* pcNamedArgs)
   at System.Reflection.CustomAttribute.CreateCaObject(RuntimeModule module, RuntimeType type, IRuntimeMethodInfo ctor, IntPtr& blob, IntPtr blobEnd, Int32& namedArgs)
   at System.Reflection.CustomAttribute.AddCustomAttributes(ListBuilder`1& attributes, RuntimeModule decoratedModule, Int32 decoratedMetadataToken, RuntimeType attributeFilterType, Boolean mustBeInheritable, ListBuilder`1 derivedAttributes)
   at System.Reflection.CustomAttribute.GetCustomAttributes(RuntimeModule decoratedModule, Int32 decoratedMetadataToken, Int32 pcaCount, RuntimeType attributeFilterType)
   at System.Reflection.CustomAttribute.GetCustomAttributes(RuntimeMethodInfo method, RuntimeType caType, Boolean inherit)
   at System.Reflection.RuntimeMethodInfo.GetCustomAttributes(Type attributeType, Boolean inherit)
   at System.Attribute.GetCustomAttributes(MemberInfo element, Type attributeType, Boolean inherit)
   at System.Reflection.CustomAttributeExtensions.GetCustomAttributes[T](MemberInfo element, Boolean inherit)
   at System.Diagnostics.StackTrace.TryResolveStateMachineMethod(MethodBase& method, Type& declaringType)
   at System.Diagnostics.StackTrace.ToString(TraceFormat traceFormat, StringBuilder sb)
   at System.Diagnostics.StackTrace.ToString(TraceFormat traceFormat)
   at System.Exception.get_StackTrace()
   at System.Exception.ToString()
   at log4net.ObjectRenderer.DefaultRenderer.RenderObject(RendererMap rendererMap, Object obj, TextWriter writer)
   at log4net.ObjectRenderer.RendererMap.FindAndRender(Object obj, TextWriter writer)</stackTrace></log4net.Error>
2025-05-28 14:35:34,742 [126] ERROR Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware [(null)] - An exception was thrown attempting to display the error page.
System.TypeLoadException: Could not load type 'AspNetCoreGeneratedDocument.Views_Home_Index+<ExecuteAsync>d__9#1' from assembly 'OpenAuth.Customer.Mvc, Version=*******, Culture=neutral, PublicKeyToken=null'.
   at System.Reflection.CustomAttribute._CreateCaObject(RuntimeModule pModule, RuntimeType type, IRuntimeMethodInfo pCtor, Byte** ppBlob, Byte* pEndBlob, Int32* pcNamedArgs)
   at System.Reflection.CustomAttribute.CreateCaObject(RuntimeModule module, RuntimeType type, IRuntimeMethodInfo ctor, IntPtr& blob, IntPtr blobEnd, Int32& namedArgs)
   at System.Reflection.CustomAttribute.AddCustomAttributes(ListBuilder`1& attributes, RuntimeModule decoratedModule, Int32 decoratedMetadataToken, RuntimeType attributeFilterType, Boolean mustBeInheritable, ListBuilder`1 derivedAttributes)
   at System.Reflection.CustomAttribute.GetCustomAttributes(RuntimeMethodInfo method, RuntimeType caType, Boolean inherit)
   at System.Reflection.RuntimeMethodInfo.GetCustomAttributes(Type attributeType, Boolean inherit)
   at System.Attribute.GetCustomAttributes(MemberInfo element, Type attributeType, Boolean inherit)
   at System.Reflection.CustomAttributeExtensions.GetCustomAttributes[T](MemberInfo element)
   at Microsoft.Extensions.StackTrace.Sources.StackTraceHelper.TryResolveStateMachineMethod(MethodBase& method, Type& declaringType)
   at Microsoft.Extensions.StackTrace.Sources.StackTraceHelper.GetMethodDisplayString(MethodBase method)
   at Microsoft.Extensions.StackTrace.Sources.StackTraceHelper.GetFrames(Exception exception, AggregateException& error)
   at Microsoft.Extensions.StackTrace.Sources.ExceptionDetailsProvider.GetStackFrames(Exception original)
   at Microsoft.Extensions.StackTrace.Sources.ExceptionDetailsProvider.GetDetails(Exception exception)+MoveNext()
   at Microsoft.AspNetCore.Diagnostics.RazorViews.ErrorPage.ExecuteAsync()
   at Microsoft.Extensions.RazorViews.BaseView.ExecuteAsync(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-05-28 14:35:34,757 [126] ERROR Microsoft.AspNetCore.Server.Kestrel [(null)] - Connection id "0HNCTLSDICD9F", Request id "0HNCTLSDICD9F:00000002": An unhandled exception was thrown by the application.
<log4net.Error>Exception rendering object type [Microsoft.CSharp.RuntimeBinder.RuntimeBinderException]<stackTrace>System.TypeLoadException: Could not load type 'AspNetCoreGeneratedDocument.Views_Home_Index+<ExecuteAsync>d__9#1' from assembly 'OpenAuth.Customer.Mvc, Version=*******, Culture=neutral, PublicKeyToken=null'.
   at System.Reflection.CustomAttribute._CreateCaObject(RuntimeModule pModule, RuntimeType type, IRuntimeMethodInfo pCtor, Byte** ppBlob, Byte* pEndBlob, Int32* pcNamedArgs)
   at System.Reflection.CustomAttribute.CreateCaObject(RuntimeModule module, RuntimeType type, IRuntimeMethodInfo ctor, IntPtr& blob, IntPtr blobEnd, Int32& namedArgs)
   at System.Reflection.CustomAttribute.AddCustomAttributes(ListBuilder`1& attributes, RuntimeModule decoratedModule, Int32 decoratedMetadataToken, RuntimeType attributeFilterType, Boolean mustBeInheritable, ListBuilder`1 derivedAttributes)
   at System.Reflection.CustomAttribute.GetCustomAttributes(RuntimeModule decoratedModule, Int32 decoratedMetadataToken, Int32 pcaCount, RuntimeType attributeFilterType)
   at System.Reflection.CustomAttribute.GetCustomAttributes(RuntimeMethodInfo method, RuntimeType caType, Boolean inherit)
   at System.Reflection.RuntimeMethodInfo.GetCustomAttributes(Type attributeType, Boolean inherit)
   at System.Attribute.GetCustomAttributes(MemberInfo element, Type attributeType, Boolean inherit)
   at System.Reflection.CustomAttributeExtensions.GetCustomAttributes[T](MemberInfo element, Boolean inherit)
   at System.Diagnostics.StackTrace.TryResolveStateMachineMethod(MethodBase& method, Type& declaringType)
   at System.Diagnostics.StackTrace.ToString(TraceFormat traceFormat, StringBuilder sb)
   at System.Diagnostics.StackTrace.ToString(TraceFormat traceFormat)
   at System.Exception.get_StackTrace()
   at System.Exception.ToString()
   at log4net.ObjectRenderer.DefaultRenderer.RenderObject(RendererMap rendererMap, Object obj, TextWriter writer)
   at log4net.ObjectRenderer.RendererMap.FindAndRender(Object obj, TextWriter writer)</stackTrace></log4net.Error>
2025-05-28 14:35:36,706 [133] ERROR Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware [(null)] - An unhandled exception has occurred while executing the request.
<log4net.Error>Exception rendering object type [Microsoft.CSharp.RuntimeBinder.RuntimeBinderException]<stackTrace>System.TypeLoadException: Could not load type 'AspNetCoreGeneratedDocument.Views_Home_Index+<ExecuteAsync>d__9#1' from assembly 'OpenAuth.Customer.Mvc, Version=*******, Culture=neutral, PublicKeyToken=null'.
   at System.Reflection.CustomAttribute._CreateCaObject(RuntimeModule pModule, RuntimeType type, IRuntimeMethodInfo pCtor, Byte** ppBlob, Byte* pEndBlob, Int32* pcNamedArgs)
   at System.Reflection.CustomAttribute.CreateCaObject(RuntimeModule module, RuntimeType type, IRuntimeMethodInfo ctor, IntPtr& blob, IntPtr blobEnd, Int32& namedArgs)
   at System.Reflection.CustomAttribute.AddCustomAttributes(ListBuilder`1& attributes, RuntimeModule decoratedModule, Int32 decoratedMetadataToken, RuntimeType attributeFilterType, Boolean mustBeInheritable, ListBuilder`1 derivedAttributes)
   at System.Reflection.CustomAttribute.GetCustomAttributes(RuntimeModule decoratedModule, Int32 decoratedMetadataToken, Int32 pcaCount, RuntimeType attributeFilterType)
   at System.Reflection.CustomAttribute.GetCustomAttributes(RuntimeMethodInfo method, RuntimeType caType, Boolean inherit)
   at System.Reflection.RuntimeMethodInfo.GetCustomAttributes(Type attributeType, Boolean inherit)
   at System.Attribute.GetCustomAttributes(MemberInfo element, Type attributeType, Boolean inherit)
   at System.Reflection.CustomAttributeExtensions.GetCustomAttributes[T](MemberInfo element, Boolean inherit)
   at System.Diagnostics.StackTrace.TryResolveStateMachineMethod(MethodBase& method, Type& declaringType)
   at System.Diagnostics.StackTrace.ToString(TraceFormat traceFormat, StringBuilder sb)
   at System.Diagnostics.StackTrace.ToString(TraceFormat traceFormat)
   at System.Exception.get_StackTrace()
   at System.Exception.ToString()
   at log4net.ObjectRenderer.DefaultRenderer.RenderObject(RendererMap rendererMap, Object obj, TextWriter writer)
   at log4net.ObjectRenderer.RendererMap.FindAndRender(Object obj, TextWriter writer)</stackTrace></log4net.Error>
2025-05-28 14:35:36,714 [133] ERROR Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware [(null)] - An exception was thrown attempting to display the error page.
System.TypeLoadException: Could not load type 'AspNetCoreGeneratedDocument.Views_Home_Index+<ExecuteAsync>d__9#1' from assembly 'OpenAuth.Customer.Mvc, Version=*******, Culture=neutral, PublicKeyToken=null'.
   at System.Reflection.CustomAttribute._CreateCaObject(RuntimeModule pModule, RuntimeType type, IRuntimeMethodInfo pCtor, Byte** ppBlob, Byte* pEndBlob, Int32* pcNamedArgs)
   at System.Reflection.CustomAttribute.CreateCaObject(RuntimeModule module, RuntimeType type, IRuntimeMethodInfo ctor, IntPtr& blob, IntPtr blobEnd, Int32& namedArgs)
   at System.Reflection.CustomAttribute.AddCustomAttributes(ListBuilder`1& attributes, RuntimeModule decoratedModule, Int32 decoratedMetadataToken, RuntimeType attributeFilterType, Boolean mustBeInheritable, ListBuilder`1 derivedAttributes)
   at System.Reflection.CustomAttribute.GetCustomAttributes(RuntimeMethodInfo method, RuntimeType caType, Boolean inherit)
   at System.Reflection.RuntimeMethodInfo.GetCustomAttributes(Type attributeType, Boolean inherit)
   at System.Attribute.GetCustomAttributes(MemberInfo element, Type attributeType, Boolean inherit)
   at System.Reflection.CustomAttributeExtensions.GetCustomAttributes[T](MemberInfo element)
   at Microsoft.Extensions.StackTrace.Sources.StackTraceHelper.TryResolveStateMachineMethod(MethodBase& method, Type& declaringType)
   at Microsoft.Extensions.StackTrace.Sources.StackTraceHelper.GetMethodDisplayString(MethodBase method)
   at Microsoft.Extensions.StackTrace.Sources.StackTraceHelper.GetFrames(Exception exception, AggregateException& error)
   at Microsoft.Extensions.StackTrace.Sources.ExceptionDetailsProvider.GetStackFrames(Exception original)
   at Microsoft.Extensions.StackTrace.Sources.ExceptionDetailsProvider.GetDetails(Exception exception)+MoveNext()
   at Microsoft.AspNetCore.Diagnostics.RazorViews.ErrorPage.ExecuteAsync()
   at Microsoft.Extensions.RazorViews.BaseView.ExecuteAsync(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-05-28 14:35:36,722 [133] ERROR Microsoft.AspNetCore.Server.Kestrel [(null)] - Connection id "0HNCTLSDICD9F", Request id "0HNCTLSDICD9F:00000003": An unhandled exception was thrown by the application.
<log4net.Error>Exception rendering object type [Microsoft.CSharp.RuntimeBinder.RuntimeBinderException]<stackTrace>System.TypeLoadException: Could not load type 'AspNetCoreGeneratedDocument.Views_Home_Index+<ExecuteAsync>d__9#1' from assembly 'OpenAuth.Customer.Mvc, Version=*******, Culture=neutral, PublicKeyToken=null'.
   at System.Reflection.CustomAttribute._CreateCaObject(RuntimeModule pModule, RuntimeType type, IRuntimeMethodInfo pCtor, Byte** ppBlob, Byte* pEndBlob, Int32* pcNamedArgs)
   at System.Reflection.CustomAttribute.CreateCaObject(RuntimeModule module, RuntimeType type, IRuntimeMethodInfo ctor, IntPtr& blob, IntPtr blobEnd, Int32& namedArgs)
   at System.Reflection.CustomAttribute.AddCustomAttributes(ListBuilder`1& attributes, RuntimeModule decoratedModule, Int32 decoratedMetadataToken, RuntimeType attributeFilterType, Boolean mustBeInheritable, ListBuilder`1 derivedAttributes)
   at System.Reflection.CustomAttribute.GetCustomAttributes(RuntimeModule decoratedModule, Int32 decoratedMetadataToken, Int32 pcaCount, RuntimeType attributeFilterType)
   at System.Reflection.CustomAttribute.GetCustomAttributes(RuntimeMethodInfo method, RuntimeType caType, Boolean inherit)
   at System.Reflection.RuntimeMethodInfo.GetCustomAttributes(Type attributeType, Boolean inherit)
   at System.Attribute.GetCustomAttributes(MemberInfo element, Type attributeType, Boolean inherit)
   at System.Reflection.CustomAttributeExtensions.GetCustomAttributes[T](MemberInfo element, Boolean inherit)
   at System.Diagnostics.StackTrace.TryResolveStateMachineMethod(MethodBase& method, Type& declaringType)
   at System.Diagnostics.StackTrace.ToString(TraceFormat traceFormat, StringBuilder sb)
   at System.Diagnostics.StackTrace.ToString(TraceFormat traceFormat)
   at System.Exception.get_StackTrace()
   at System.Exception.ToString()
   at log4net.ObjectRenderer.DefaultRenderer.RenderObject(RendererMap rendererMap, Object obj, TextWriter writer)
   at log4net.ObjectRenderer.RendererMap.FindAndRender(Object obj, TextWriter writer)</stackTrace></log4net.Error>
2025-05-28 14:35:37,620 [126] ERROR Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware [(null)] - An unhandled exception has occurred while executing the request.
<log4net.Error>Exception rendering object type [Microsoft.CSharp.RuntimeBinder.RuntimeBinderException]<stackTrace>System.TypeLoadException: Could not load type 'AspNetCoreGeneratedDocument.Views_Home_Index+<ExecuteAsync>d__9#1' from assembly 'OpenAuth.Customer.Mvc, Version=*******, Culture=neutral, PublicKeyToken=null'.
   at System.Reflection.CustomAttribute._CreateCaObject(RuntimeModule pModule, RuntimeType type, IRuntimeMethodInfo pCtor, Byte** ppBlob, Byte* pEndBlob, Int32* pcNamedArgs)
   at System.Reflection.CustomAttribute.CreateCaObject(RuntimeModule module, RuntimeType type, IRuntimeMethodInfo ctor, IntPtr& blob, IntPtr blobEnd, Int32& namedArgs)
   at System.Reflection.CustomAttribute.AddCustomAttributes(ListBuilder`1& attributes, RuntimeModule decoratedModule, Int32 decoratedMetadataToken, RuntimeType attributeFilterType, Boolean mustBeInheritable, ListBuilder`1 derivedAttributes)
   at System.Reflection.CustomAttribute.GetCustomAttributes(RuntimeModule decoratedModule, Int32 decoratedMetadataToken, Int32 pcaCount, RuntimeType attributeFilterType)
   at System.Reflection.CustomAttribute.GetCustomAttributes(RuntimeMethodInfo method, RuntimeType caType, Boolean inherit)
   at System.Reflection.RuntimeMethodInfo.GetCustomAttributes(Type attributeType, Boolean inherit)
   at System.Attribute.GetCustomAttributes(MemberInfo element, Type attributeType, Boolean inherit)
   at System.Reflection.CustomAttributeExtensions.GetCustomAttributes[T](MemberInfo element, Boolean inherit)
   at System.Diagnostics.StackTrace.TryResolveStateMachineMethod(MethodBase& method, Type& declaringType)
   at System.Diagnostics.StackTrace.ToString(TraceFormat traceFormat, StringBuilder sb)
   at System.Diagnostics.StackTrace.ToString(TraceFormat traceFormat)
   at System.Exception.get_StackTrace()
   at System.Exception.ToString()
   at log4net.ObjectRenderer.DefaultRenderer.RenderObject(RendererMap rendererMap, Object obj, TextWriter writer)
   at log4net.ObjectRenderer.RendererMap.FindAndRender(Object obj, TextWriter writer)</stackTrace></log4net.Error>
2025-05-28 14:35:37,632 [126] ERROR Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware [(null)] - An exception was thrown attempting to display the error page.
System.TypeLoadException: Could not load type 'AspNetCoreGeneratedDocument.Views_Home_Index+<ExecuteAsync>d__9#1' from assembly 'OpenAuth.Customer.Mvc, Version=*******, Culture=neutral, PublicKeyToken=null'.
   at System.Reflection.CustomAttribute._CreateCaObject(RuntimeModule pModule, RuntimeType type, IRuntimeMethodInfo pCtor, Byte** ppBlob, Byte* pEndBlob, Int32* pcNamedArgs)
   at System.Reflection.CustomAttribute.CreateCaObject(RuntimeModule module, RuntimeType type, IRuntimeMethodInfo ctor, IntPtr& blob, IntPtr blobEnd, Int32& namedArgs)
   at System.Reflection.CustomAttribute.AddCustomAttributes(ListBuilder`1& attributes, RuntimeModule decoratedModule, Int32 decoratedMetadataToken, RuntimeType attributeFilterType, Boolean mustBeInheritable, ListBuilder`1 derivedAttributes)
   at System.Reflection.CustomAttribute.GetCustomAttributes(RuntimeMethodInfo method, RuntimeType caType, Boolean inherit)
   at System.Reflection.RuntimeMethodInfo.GetCustomAttributes(Type attributeType, Boolean inherit)
   at System.Attribute.GetCustomAttributes(MemberInfo element, Type attributeType, Boolean inherit)
   at System.Reflection.CustomAttributeExtensions.GetCustomAttributes[T](MemberInfo element)
   at Microsoft.Extensions.StackTrace.Sources.StackTraceHelper.TryResolveStateMachineMethod(MethodBase& method, Type& declaringType)
   at Microsoft.Extensions.StackTrace.Sources.StackTraceHelper.GetMethodDisplayString(MethodBase method)
   at Microsoft.Extensions.StackTrace.Sources.StackTraceHelper.GetFrames(Exception exception, AggregateException& error)
   at Microsoft.Extensions.StackTrace.Sources.ExceptionDetailsProvider.GetStackFrames(Exception original)
   at Microsoft.Extensions.StackTrace.Sources.ExceptionDetailsProvider.GetDetails(Exception exception)+MoveNext()
   at Microsoft.AspNetCore.Diagnostics.RazorViews.ErrorPage.ExecuteAsync()
   at Microsoft.Extensions.RazorViews.BaseView.ExecuteAsync(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-05-28 14:35:37,634 [126] ERROR Microsoft.AspNetCore.Server.Kestrel [(null)] - Connection id "0HNCTLSDICD9F", Request id "0HNCTLSDICD9F:00000004": An unhandled exception was thrown by the application.
<log4net.Error>Exception rendering object type [Microsoft.CSharp.RuntimeBinder.RuntimeBinderException]<stackTrace>System.TypeLoadException: Could not load type 'AspNetCoreGeneratedDocument.Views_Home_Index+<ExecuteAsync>d__9#1' from assembly 'OpenAuth.Customer.Mvc, Version=*******, Culture=neutral, PublicKeyToken=null'.
   at System.Reflection.CustomAttribute._CreateCaObject(RuntimeModule pModule, RuntimeType type, IRuntimeMethodInfo pCtor, Byte** ppBlob, Byte* pEndBlob, Int32* pcNamedArgs)
   at System.Reflection.CustomAttribute.CreateCaObject(RuntimeModule module, RuntimeType type, IRuntimeMethodInfo ctor, IntPtr& blob, IntPtr blobEnd, Int32& namedArgs)
   at System.Reflection.CustomAttribute.AddCustomAttributes(ListBuilder`1& attributes, RuntimeModule decoratedModule, Int32 decoratedMetadataToken, RuntimeType attributeFilterType, Boolean mustBeInheritable, ListBuilder`1 derivedAttributes)
   at System.Reflection.CustomAttribute.GetCustomAttributes(RuntimeModule decoratedModule, Int32 decoratedMetadataToken, Int32 pcaCount, RuntimeType attributeFilterType)
   at System.Reflection.CustomAttribute.GetCustomAttributes(RuntimeMethodInfo method, RuntimeType caType, Boolean inherit)
   at System.Reflection.RuntimeMethodInfo.GetCustomAttributes(Type attributeType, Boolean inherit)
   at System.Attribute.GetCustomAttributes(MemberInfo element, Type attributeType, Boolean inherit)
   at System.Reflection.CustomAttributeExtensions.GetCustomAttributes[T](MemberInfo element, Boolean inherit)
   at System.Diagnostics.StackTrace.TryResolveStateMachineMethod(MethodBase& method, Type& declaringType)
   at System.Diagnostics.StackTrace.ToString(TraceFormat traceFormat, StringBuilder sb)
   at System.Diagnostics.StackTrace.ToString(TraceFormat traceFormat)
   at System.Exception.get_StackTrace()
   at System.Exception.ToString()
   at log4net.ObjectRenderer.DefaultRenderer.RenderObject(RendererMap rendererMap, Object obj, TextWriter writer)
   at log4net.ObjectRenderer.RendererMap.FindAndRender(Object obj, TextWriter writer)</stackTrace></log4net.Error>
2025-05-28 14:36:53,403 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (5ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 14:36:53,675 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:36:53,677 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:36:53,685 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:37:11,538 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:37:11,540 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:37:12,507 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:47:04,059 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (5ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 14:47:04,408 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:47:04,410 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:47:04,415 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:47:04,909 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:47:04,948 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:47:04,952 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (5ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:47:14,693 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:47:14,693 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:47:14,694 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:47:14,998 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:47:15,087 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:47:15,087 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:47:18,369 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:47:18,369 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:48:10,386 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:48:10,386 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:48:15,758 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:48:15,758 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:49:48,158 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:49:48,159 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:49:48,158 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:50:29,148 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (5ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 14:50:29,430 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:50:29,433 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:50:29,450 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:50:34,193 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:50:34,193 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:51:26,759 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:51:26,760 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:51:26,760 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:51:28,103 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:51:28,103 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:51:31,859 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:52:18,967 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 14:52:20,202 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:52:20,202 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:52:20,203 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:52:23,118 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:52:23,118 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 14:52:24,553 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:11:39,380 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (6ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 15:11:39,409 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 15:11:40,046 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:11:40,046 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:11:40,057 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:13:32,414 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:13:32,414 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:13:32,414 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:13:33,523 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:13:33,527 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:13:33,527 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:14:55,962 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (8ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 15:14:56,522 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:14:56,524 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:14:56,527 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:15:48,791 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:15:48,791 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:15:48,796 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (5ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:15:49,988 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:15:49,988 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:15:49,988 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:15:50,495 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:15:50,495 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:15:50,497 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:15:51,022 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:15:51,022 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:15:51,022 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:15:51,490 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:15:51,490 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:15:51,490 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:15:51,999 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:15:51,999 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:15:52,000 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:15:52,622 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:15:52,623 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:15:52,623 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:16:55,318 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:16:55,318 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:16:55,318 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:16:57,281 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:16:57,281 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:16:57,282 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:16:58,434 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:16:58,434 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:16:58,434 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:16:59,386 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:16:59,386 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:16:59,386 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:17:00,071 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:17:00,071 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:17:00,071 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:17:00,692 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:17:00,693 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:17:00,693 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:17:18,425 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:17:18,428 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:17:18,428 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:17:47,608 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:17:47,631 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:17:47,634 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:20:41,563 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:20:41,563 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:20:41,566 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:21:00,981 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 15:21:01,038 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:21:01,038 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:21:01,038 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:23:13,477 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:23:13,477 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:23:13,477 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:26:14,227 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:26:14,227 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:26:14,247 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (21ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:26:20,721 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:26:20,721 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:26:20,721 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:26:24,165 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:26:24,168 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:26:24,168 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:28:22,686 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (4ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:28:22,686 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (4ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:28:22,686 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (4ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:28:37,054 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:28:37,054 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:28:37,055 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:29:19,925 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:29:19,926 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:29:19,926 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:29:43,012 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:29:43,012 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:29:43,012 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:30:07,655 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:30:07,655 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:30:07,656 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:31:41,816 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:31:41,816 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:31:41,816 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:32:32,348 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (5ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 15:32:32,348 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (5ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 15:32:33,925 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:32:33,925 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:32:33,928 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:33:38,249 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:33:38,249 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:33:38,252 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (4ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:35:25,619 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:35:25,619 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:35:25,620 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:36:01,576 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:36:01,576 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:36:01,576 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:36:12,621 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (4ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:36:12,624 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:36:12,621 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (4ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:36:34,245 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (7ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:36:34,245 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (7ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:36:34,266 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:36:44,123 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:36:44,124 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:36:44,125 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:38:20,233 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:38:20,245 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:38:20,245 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:38:28,931 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:38:28,931 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:38:28,931 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:38:50,719 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:38:50,728 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:38:50,730 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:39:24,147 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (6ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 15:39:24,147 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (6ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 15:39:24,695 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:39:24,695 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:39:24,700 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:39:31,307 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:39:31,307 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:39:31,308 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (4ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:39:38,158 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 15:39:38,946 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:39:38,947 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:39:38,947 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:40:18,859 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:40:18,867 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:40:18,868 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:41:11,991 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 15:41:11,991 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 15:41:11,991 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (7ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 15:41:11,991 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 15:41:11,991 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (5ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 15:41:12,805 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:41:12,805 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:41:12,806 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:41:26,724 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:41:26,724 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:41:26,729 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:42:33,707 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:42:33,710 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:42:33,710 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:44:44,057 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (5ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 15:44:44,056 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (5ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 15:44:44,174 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 15:44:44,508 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:44:44,508 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:44:44,508 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:44:57,624 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 15:44:58,277 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:44:58,283 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:44:58,284 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:45:06,203 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 15:45:06,689 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:45:06,691 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (5ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:45:06,690 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (5ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:47:40,695 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (5ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 15:47:40,695 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (5ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 15:47:41,197 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:47:41,197 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:47:41,199 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:48:01,382 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__userInfo_Name_0='JOLY Web CS' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_Name_0
2025-05-28 15:48:02,747 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:48:02,748 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:48:02,747 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:48:04,514 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:48:04,514 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-05-28 15:48:06,241 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='JOLY' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
