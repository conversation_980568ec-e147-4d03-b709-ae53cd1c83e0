/*公共样式*/
.main_body {
	min-width: 320px;
}
.layui-elem-quote.title{ padding:10px 15px; margin-bottom:0; }
.layui-tab-more{ position: relative; z-index: 99; background:#fff; }
.layui-layer-tab .layui-layer-title span.layui-layer-tabnow{ height:42px !important; }
.layui-layer-tab .layui-layer-title span{ min-width:45px !important; }
.marg0{ margin:0; }
/*模拟加载层图标样式*/
.layui-layer-dialog .layui-layer-content .layui-layer-ico16{ background-size:100% 100% !important; }

.layui-header {
    height: 50px;
}

.layui-layout-admin .layui-body {
    top: 50px;
}

.layui-layout-admin .layui-side {
    top: 50px;
}

.layui-nav .layui-nav-item {
    line-height: 50px;
}

/*样式改变的过渡*/
.showMenu .layui-body,.showMenu .layui-footer,.showMenu.layui-layout-admin .layui-side,.logo,.top_menu .layui-nav-item[pc],.component,.top_menu .layui-nav-item[mobile],.layui-nav,.layui-layout-admin .layui-main,.site-mobile .layui-side,.layui-layout-admin .layui-side,.site-mobile .site-tree-mobile,.layui-body,.layui-layout-admin .layui-footer,.layui-layout-admin .layui-side,.panel,.panel .panel_icon i{ transition: all 0.3s ease-in-out;-webkit-transition: all 0.3s ease-in-out;-o-transition: all 0.3s ease-in-out;-moz-transition: all 0.3s ease-in-out;-ms-transition: all 0.3s ease-in-out; }


.showMenu.layui-layout-admin .layui-side{ left:-200px; }
.showMenu .layui-body,.showMenu .layui-footer{ left:0; }
.layui-layout-admin .layui-main{ margin:0; }
.logo{
	color: #fff;
	float: left;
	line-height:50px;
	font-size:22px;
	font-weight: 600;
	padding:0 25px;
	text-align: center;
	width:180px;
	text-shadow: 0 1px 3px rgba(0,0,0,0.3);
	letter-spacing: 1px;
}
.hideMenu{
	float:left;
	width:20px;
	height:20px;
	margin:10px 15px 0 0;
	font-size:17px;
	text-align:center;
	padding:5px 5px;
	color:#fff;
	background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
	border-radius: 4px;
	transition: all 0.3s ease;
	box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}
.hideMenu:hover{
	color:#fff;
	background: linear-gradient(135deg, #2980b9 0%, #3498db 100%);
	transform: translateY(-1px);
	box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}
.weather{ color:#fff; float:left; margin:10px 0 0 50px;}
.component{ float: left; width:200px; height:30px; margin-top: 10px; position: relative;}
.component .layui-input{ height:30px; line-height: 30px; font-size:12px; border:none; transition: all 0.3s; }
.component .layui-input:focus{ background:#fff; color:#000; }
.component .layui-form-select dl{ top:33px; background:#fff; }
.component .layui-form-select .layui-edge,.top_menu .layui-nav-item[mobile]{ display:none; }
.component .layui-icon{ position: absolute; right:8px; top:8px; color:#000; }

/*顶部右侧导航现代化样式*/
.layui-nav .layui-nav-item>a{
	color:#fff;
	transition: all 0.3s ease;
}
.top_menu{
	position:absolute;
	right:0;
	background:none;
}
.top_menu.layui-nav .layui-this:after{ width:0px; }
.top_menu.layui-nav .layui-this,.closeBox.layui-nav .layui-this{
	background-color:transparent;
}
.top_menu.layui-nav .layui-this a,.closeBox.layui-nav .layui-this a{
	color:#ecf0f1;
	background: rgba(52, 152, 219, 0.2);
	border-radius: 4px;
}
.top_menu.layui-nav dd.layui-this a,.closeBox.layui-nav dd.layui-this a{
	color:#2c3e50;
	background: #ecf0f1;
	border-radius: 4px;
}
.top_menu.layui-nav .layui-nav-child a:hover,.closeBox.layui-nav .layui-nav-child a:hover{
	color:#fff;
	background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
	border-radius: 4px;
	transform: translateY(-1px);
	box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}
.top_menu .layui-nav-item a:hover {
	background: rgba(255,255,255,0.1);
	border-radius: 4px;
	transform: translateY(-1px);
}
.top_menu .iconfont{
	font-size: 14px !important;
	margin-right: 4px;
}
.top_menu .layui-nav-bar{
	top:60px !important;
	background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
	border-radius: 4px;
}

/*左侧用户头像*/
.layui-nav{ background-color:inherit !important; }
.layui-layout-admin .layui-side{ left:0; }
.user-photo{width: 200px; height: 120px; padding-top: 15px; padding-bottom: 5px;}
.user-photo a.img{ display: block; width: 76px; height: 76px; margin: 0 auto; margin-bottom: 15px;}
.user-photo a.img img{ display: block; border: none; width: 100%; height: 100%; border-radius: 50%; -webkit-border-radius: 50%; -moz-border-radius: 50%; border: 4px solid #44576b;}
.user-photo p{ display: block; width: 100%; height: 25px; color: #ffffff; text-align: center; font-size: 12px; white-space: nowrap;line-height: 25px; overflow: hidden;}
/*左侧导航重定义*/
.layui-nav-item a cite{ padding:0 5px; }
.layui-side-scroll{ height:auto; }
.layui-nav-tree .layui-nav-child a{ padding-left: 40px; }
.layui-nav-tree .layui-nav-child a:hover{ background-color:#4E5465; }
.layui-nav-tree .layui-nav-child dd.layui-this a:hover{ background-color:#009688; }

/*右侧body*/
#top_tabs_box{ padding-right:138px; height:40px; border-bottom:1px solid #e2e2e2; }
#top_tabs{ position: absolute; border-bottom:none;}
/*多窗口页面操作下拉*/
.closeBox{ position:absolute; right:0; background-color:#fff !important; color:#000; border-left:1px solid #e2e2e2; border-bottom:1px solid #e2e2e2; }
.closeBox .layui-nav-item{ line-height:40px; }
.closeBox .layui-nav-item a,.closeBox .layui-nav-item a:hover{ color:#000; }
.closeBox .layui-nav-more{ top:17px; }
.closeBox .layui-nav-mored{ top:11px; }
.closeBox .layui-nav-child{ top:42px; left:-12px; }
.closeBox .layui-nav-bar{ display:none; }
.closeBox .icon-caozuo{ font-size: 20px; position:absolute; top:1px; left:-2px; }

.layui-body{
	overflow:hidden;
	border-top:4px solid var(--primary-color, #3498db);
	border-left:2px solid var(--primary-color, #3498db);
	background: #f8f9fa;
}
.layui-tab-content{ height:100%; padding:0; }
.layui-tab-item{ position: absolute; top: 41px; bottom:0; left: 0; right: 0; padding: 0; margin: 0; -webkit-overflow-scrolling:touch; overflow:auto;}
.layui-tab-title .layui-this{ background-color:#18A093;}
.layui-tab-title .layui-this:after{ border:none; }
.layui-tab-title li cite{ font-style: normal; padding-left:5px; }
.layui-tab-card .layui-tab-title{background-color: #ffffff}
.layui-tab-card .layui-tab-title .layui-this{background-color: #f2f2f2}
.clildFrame.layui-tab-content{ padding-right: 0; }
.clildFrame.layui-tab-content iframe{ width: 100%; height:100%; border:none; min-width: 320px; position:absolute; }
/*main.html*/
.row,.col,.panel_word,.panel_icon{ box-sizing:border-box; -webkit-box-sizing:border-box; -moz-box-sizing:border-box; -o-box-sizing:border-box;}
.row{ margin-left:-10px; overflow:hidden;display: flex;    flex-wrap: wrap;}
.col{ padding-left:10px;}
.panel{float: left; text-align: center; width:16.666%; min-width:210px; margin-bottom: 10px;}
.panel_box a{display:block; background-color:#f2f2f2; border-radius:5px; overflow:hidden; }
.panel_icon{ width:40%; display: inline-block; padding:22px 0; background-color:#54ade8;float:left;}
.panel_icon i{ font-size:3em; color:#fff;}
.panel a:hover .panel_icon i{ display:inline-block; transform:rotate(360deg); -webkit-transform:rotate(360deg); -moz-transform:rotate(360deg); -o-transform:rotate(360deg); -ms-transform:rotate(360deg);}
.panel_word{ width:60%; display: inline-block; float:right; margin-top: 22px; }
.panel_word span{ font-size:25px; display:block; height:30px; line-height:30px; }
.allNews em{ font-style:normal; font-size:16px;display: block; }
.panel_box a .allNews cite{ display:none; }
.panel_box a cite{ font-size:16px; display: block; font-style:normal; }
.sysNotice{ width:50%; float: left; }
.sysNotice .layui-elem-quote{ line-height:26px; position: relative;}
.sysNotice .layui-table{ margin-top:0; border-left:5px solid #e2e2e2; }
.sysNotice .title .icon-new1{ position: absolute; top:8px; margin-left: 10px; color:#f00; font-size:25px; }
.explain .layui-btn{ margin:5px 5px 5px 0; }

/*打开页面动画*/
.layui-tab-item.layui-show{ animation:moveTop 1s; -webkit-animation:moveTop 1s; animation-fill-mode:both; -webkit-animation-fill-mode:both; }
@keyframes moveTop{
    0% {opacity: 0;-webkit-transform: translateY(20px);-ms-transform: translateY(20px);transform: translateY(20px);}
    100% {opacity: 1;-webkit-transform: translateY(0);-ms-transform: translateY(0);transform: translateY(0);}
}
@-o-keyframes moveTop{
    0% {opacity: 0;-webkit-transform: translateY(20px);-ms-transform: translateY(20px);transform: translateY(20px);}
    100% {opacity: 1;-webkit-transform: translateY(0);-ms-transform: translateY(0);transform: translateY(0);}
}
@-moz-keyframes moveTop{
    0% {opacity: 0;-webkit-transform: translateY(20px);-ms-transform: translateY(20px);transform: translateY(20px);}
    100% {opacity: 1;-webkit-transform: translateY(0);-ms-transform: translateY(0);transform: translateY(0);}
}
@-webkit-keyframes moveTop{
    0% {opacity: 0;-webkit-transform: translateY(20px);-ms-transform: translateY(20px);transform: translateY(20px);}
    100% {opacity: 1;-webkit-transform: translateY(0);-ms-transform: translateY(0);transform: translateY(0);}
}

/*锁屏*/
.admin-header-lock{width: 320px; height: 170px; padding: 20px; position: relative; text-align: center;}
.admin-header-lock-img{width: 60px; height: 60px; margin: 0 auto;}
.admin-header-lock-img img{width: 60px; height: 60px; border-radius: 100%;}
.admin-header-lock-name{color: #009688;margin: 8px 0 15px 0;}
.input_btn{ overflow: hidden; margin-bottom: 10px; }
.admin-header-lock-input{width: 170px; color: #fff;background-color: #009688; float: left; margin:0 10px 0 40px; border:none;}
.admin-header-lock-input::-webkit-input-placeholder {color:#fff;}
.admin-header-lock-input::-moz-placeholder {color:#fff;}
.admin-header-lock-input::-ms-input-placeholder {color:#fff;}
.admin-header-lock-input:-moz-placeholder {color:#fff;}
#unlock{ float: left; }
#lock-box p{ color:#e60000; }

/*换肤*/
.skins_box{ padding:10px 34px 0; }
.skinBtn{ text-align:right; }
/*橙色*/
.orange .layui-layout-admin .layui-header{ background-color:orange!important; }
.orange .layui-bg-black{ background-color:#e47214!important; }
/*蓝色*/
.blue .layui-layout-admin .layui-header{ background-color:#3396d8!important; }
.blue .layui-bg-black,.blue .hideMenu{ background-color:#146aa2!important; }
/*现代化深色主题*/
.cyan .layui-layout-admin .layui-header {
	background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
	box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
	border-bottom: 1px solid rgba(255,255,255,0.1) !important;
}

.cyan .layui-bg-black, .cyan .hideMenu {
	background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%) !important;
	border-right: 1px solid rgba(255,255,255,0.1) !important;
}
/*天蓝*/
.skyBlue .layui-layout-admin .layui-header {
	background-color: #87CEEB !important;
}
.skyBlue .layui-bg-black, .skyBlue .hideMenu {
	background-color: #87CEEB !important;
}

/*自定义*/
/*.skinCustom{ visibility:hidden; }
.skinCustom input{ width:48%; margin:5px 2% 5px 0; float:left; }*/

.orange .layui-nav-tree .layui-nav-child a,.blue .layui-nav-tree .layui-nav-child a{ color:#fff; }
.orange .top_menu.layui-nav .layui-nav-more,.blue .top_menu.layui-nav .layui-nav-more{border-color:#fff transparent transparent !important;}
.orange .top_menu.layui-nav-itemed .layui-nav-more,.orange .top_menu.layui-nav .layui-nav-mored,.blue .top_menu.layui-nav-itemed .layui-nav-more,.blue .top_menu.layui-nav .layui-nav-mored{border-color:transparent transparent #fff !important;}

/*底部*/
.footer{
	text-align: center;
	line-height:44px;
	border-left: 2px solid var(--primary-color, #3498db);
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	color: var(--secondary-color, #2c3e50);
	font-weight: 500;
}


/*响应式*/
@media screen and (max-width:1282px){
	.panel{ width:33.3333%; }
}
@media screen and (max-width:1050px){
	.layui-nav.top_menu .layui-nav-item a{ padding:0 10px; }
	/*天气信息*/
	.weather[pc]{ display: none !important; }
	.sysNotice{ width:100%; }
	.component{ width:165px; }
}
@media screen and (max-width: 750px){
	.logo{ padding:0;}
	.top_menu .layui-nav-item[pc],.component,.site-mobile .site-tree-mobile{ display: none !important; }
	.top_menu .layui-nav-item.showNotice[pc]{ display:inline-block !important; }
	.top_menu .layui-nav-item[mobile]{ display:inline-block; }
	.layui-nav.top_menu,.layui-nav.top_menu .layui-nav-item a{ padding:0 10px; }
	.layui-layout-admin .layui-main{ margin-right: 0; }
	.hideMenu{ display:none; }
	/*左侧导航*/
	.layui-layout-admin .layui-side{ left:-260px; }
	.site-mobile .layui-side{ left: 0; z-index:9999; }
	.site-tree-mobile {display: block!important; position: fixed; z-index: 100000; bottom: 15px; left: 15px; width: 50px; height: 50px; line-height: 50px; border-radius: 2px; text-align: center; background-color: rgba(0,0,0,.7); color: #fff;}
	.site-mobile .site-mobile-shade { content: ''; position: fixed; top: 0; bottom: 0; left: 0; right: 0; background-color: rgba(0,0,0,.8); z-index: 999;}

	/*layui-body*/
	.panel{ width:50%; }

	.layui-body,.layui-layout-admin .layui-footer{ left:0; }
}
@media screen and (max-width:432px){
	.top_menu .layui-nav-item.showNotice[pc]{ display:none !important; }
	.panel{ width:100%; }
}

/* 现代化菜单项样式 */
ol li a {
	background-color: rgba(52, 73, 94, 0.8) !important;
	color: #ecf0f1 !important;
	border-radius: 4px !important;
	margin: 2px 8px !important;
	padding: 8px 12px !important;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
	border-left: 3px solid transparent !important;
}

ol li a:hover {
	background: linear-gradient(135deg, #3498db 0%, #2980b9 100%) !important;
	color: #ffffff !important;
	border-left: 3px solid #e74c3c !important;
	transform: translateX(4px) !important;
	box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3) !important;
}

.three_this {
	background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%) !important;
	color: #ffffff !important;
	border-left: 3px solid #f39c12 !important;
	box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4) !important;
	transform: translateX(4px) !important;
}

/* 一级菜单项现代化样式 */
.cyan .layui-nav-tree .layui-nav-item > a {
	color: #bdc3c7 !important;
	transition: all 0.3s ease !important;
	border-radius: 6px !important;
	margin: 4px 8px !important;
	padding: 12px 16px !important;
}

.cyan .layui-nav-tree .layui-nav-item > a:hover {
	background: rgba(52, 152, 219, 0.2) !important;
	color: #3498db !important;
	transform: translateX(2px) !important;
}

.cyan .layui-nav-tree .layui-nav-itemed > a,
.cyan .layui-nav-tree .layui-this > a {
	background: rgba(52, 152, 219, 0.15) !important;
	color: #3498db !important;
	border-left: 4px solid #3498db !important;
}

/* 二级菜单项样式 */
.cyan .layui-nav-tree .layui-nav-child a {
	color: #95a5a6 !important;
	padding: 10px 20px !important;
	margin: 2px 12px !important;
	border-radius: 4px !important;
	transition: all 0.3s ease !important;
}

.cyan .layui-nav-tree .layui-nav-child a:hover {
	background: rgba(46, 204, 113, 0.2) !important;
	color: #2ecc71 !important;
	transform: translateX(3px) !important;
}

/* 现代化标签页样式 */
.layui-tab-card > .layui-tab-title {
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
	border-bottom: 2px solid #dee2e6 !important;
	padding: 0 !important;
}

.layui-tab-card > .layui-tab-title .layui-this {
	background: linear-gradient(135deg, #3498db 0%, #2980b9 100%) !important;
	color: #fff !important;
	border-radius: 6px 6px 0 0 !important;
	margin: 4px 2px 0 2px !important;
	box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3) !important;
}

.layui-tab-card > .layui-tab-title li {
	background: #ffffff !important;
	margin: 4px 2px 0 2px !important;
	border-radius: 6px 6px 0 0 !important;
	transition: all 0.3s ease !important;
	border: 1px solid #dee2e6 !important;
	border-bottom: none !important;
}

.layui-tab-card > .layui-tab-title li:hover {
	background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%) !important;
	transform: translateY(-2px) !important;
	box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
}

.layui-tab-card > .layui-tab-title li a {
	color: #2c3e50 !important;
	font-weight: 500 !important;
	padding: 8px 16px !important;
}

.layui-tab-card > .layui-tab-title .layui-this a {
	color: #fff !important;
}

/* 页面操作按钮现代化 */
.closeBox.layui-nav {
	background: rgba(255,255,255,0.95) !important;
	border-radius: 6px !important;
	box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
	margin: 4px !important;
}

.closeBox.layui-nav .layui-nav-item a {
	color: #2c3e50 !important;
	font-weight: 500 !important;
	border-radius: 4px !important;
	margin: 2px !important;
	transition: all 0.3s ease !important;
}

.closeBox.layui-nav .layui-nav-item a:hover {
	background: linear-gradient(135deg, #3498db 0%, #2980b9 100%) !important;
	color: #fff !important;
	transform: translateY(-1px) !important;
}