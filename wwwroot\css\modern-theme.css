/* 现代化主题补充样式 */

/* 全局现代化变量 */
:root {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2c3e50;
    --secondary-light: #34495e;
    --accent-color: #e74c3c;
    --accent-dark: #c0392b;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --light-gray: #ecf0f1;
    --medium-gray: #bdc3c7;
    --dark-gray: #95a5a6;
    --shadow-light: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 8px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 16px rgba(0,0,0,0.2);
    --border-radius: 6px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 滚动条现代化样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(236, 240, 241, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: 4px;
    transition: var(--transition);
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
}

/* 左侧导航栏整体优化 */
.layui-side {
    box-shadow: var(--shadow-medium) !important;
    border-right: none !important;
}

/* 导航栏图标现代化 */
.layui-nav-tree .layui-nav-item a i {
    margin-right: 8px !important;
    font-size: 16px !important;
    color: var(--medium-gray) !important;
    transition: var(--transition) !important;
}

.layui-nav-tree .layui-nav-item a:hover i,
.layui-nav-tree .layui-this a i,
.layui-nav-tree .layui-nav-itemed a i {
    color: var(--primary-color) !important;
    transform: scale(1.1) !important;
}

/* 三级菜单现代化样式 */
.layui-nav-tree .layui-nav-child .layui-nav-child a {
    padding-left: 50px !important;
    color: var(--dark-gray) !important;
    font-size: 13px !important;
    position: relative !important;
}

.layui-nav-tree .layui-nav-child .layui-nav-child a:before {
    content: "•" !important;
    position: absolute !important;
    left: 35px !important;
    color: var(--primary-color) !important;
    font-weight: bold !important;
}

.layui-nav-tree .layui-nav-child .layui-nav-child a:hover {
    background: rgba(52, 152, 219, 0.1) !important;
    color: var(--primary-color) !important;
    transform: translateX(2px) !important;
}

/* 面包屑导航现代化 */
.layui-breadcrumb {
    background: var(--light-gray) !important;
    padding: 8px 16px !important;
    border-radius: var(--border-radius) !important;
    margin-bottom: 16px !important;
    box-shadow: var(--shadow-light) !important;
}

.layui-breadcrumb a {
    color: var(--primary-color) !important;
    transition: var(--transition) !important;
}

.layui-breadcrumb a:hover {
    color: var(--primary-dark) !important;
    text-decoration: none !important;
}

/* 表单元素现代化 */
.layui-form-item {
    margin-bottom: 20px !important;
}

.layui-input, .layui-textarea, .layui-select {
    border: 2px solid #e9ecef !important;
    border-radius: var(--border-radius) !important;
    transition: var(--transition) !important;
    padding: 10px 15px !important;
}

.layui-input:focus, .layui-textarea:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1) !important;
}

/* 按钮现代化样式 */
.layui-btn {
    border-radius: var(--border-radius) !important;
    font-weight: 500 !important;
    transition: var(--transition) !important;
    box-shadow: var(--shadow-light) !important;
}

.layui-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: var(--shadow-medium) !important;
}

.layui-btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)) !important;
    border-color: var(--primary-color) !important;
}

.layui-btn-danger {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-dark)) !important;
    border-color: var(--accent-color) !important;
}

/* 卡片样式现代化 */
.layui-card {
    border-radius: var(--border-radius) !important;
    box-shadow: var(--shadow-light) !important;
    border: none !important;
    transition: var(--transition) !important;
}

.layui-card:hover {
    box-shadow: var(--shadow-medium) !important;
    transform: translateY(-2px) !important;
}

.layui-card-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
    border-bottom: 2px solid var(--primary-color) !important;
    font-weight: 600 !important;
    color: var(--secondary-color) !important;
}

/* 表格现代化样式 */
.layui-table {
    border-radius: var(--border-radius) !important;
    overflow: hidden !important;
    box-shadow: var(--shadow-light) !important;
}

.layui-table thead tr {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light)) !important;
}

.layui-table thead tr th {
    color: white !important;
    font-weight: 600 !important;
    border: none !important;
}

.layui-table tbody tr:hover {
    background: rgba(52, 152, 219, 0.05) !important;
}

/* 弹窗现代化样式 */
.layui-layer {
    border-radius: var(--border-radius) !important;
    box-shadow: var(--shadow-heavy) !important;
}

.layui-layer-title {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)) !important;
    color: white !important;
    font-weight: 600 !important;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
}

/* 加载动画现代化 */
.layui-layer-loading .layui-layer-content {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: var(--border-radius) !important;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .layui-nav-tree .layui-nav-item a {
        padding: 10px 12px !important;
        font-size: 14px !important;
    }
    
    .layui-nav-tree .layui-nav-child a {
        padding: 8px 16px !important;
        font-size: 13px !important;
    }
    
    ol li a {
        padding: 6px 10px !important;
        font-size: 12px !important;
        margin: 1px 4px !important;
    }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --light-gray: #2c3e50;
        --medium-gray: #34495e;
        --dark-gray: #7f8c8d;
    }
}

/* 动画增强 */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.layui-nav-tree .layui-nav-item {
    animation: slideInLeft 0.3s ease-out !important;
}

.layui-nav-tree .layui-nav-item:nth-child(1) { animation-delay: 0.1s !important; }
.layui-nav-tree .layui-nav-item:nth-child(2) { animation-delay: 0.2s !important; }
.layui-nav-tree .layui-nav-item:nth-child(3) { animation-delay: 0.3s !important; }
.layui-nav-tree .layui-nav-item:nth-child(4) { animation-delay: 0.4s !important; }
.layui-nav-tree .layui-nav-item:nth-child(5) { animation-delay: 0.5s !important; }
