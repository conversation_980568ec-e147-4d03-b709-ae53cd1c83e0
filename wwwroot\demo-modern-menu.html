<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>精致菜单界面演示 - OWMS-OV</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/layui/css/layui.css" media="all" />
    <link rel="stylesheet" href="//at.alicdn.com/t/font_tnyc012u2rlwstt9.css" media="all" />
    <link rel="stylesheet" href="/css/main.css" media="all" />
    <style>
        .demo-container {
            padding: 20px;
            background: #f8f9fa;
            min-height: 100vh;
        }
        .demo-card {
            background: white;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .demo-title {
            color: #2c3e50;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 16px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 8px;
        }
        .color-palette {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
            margin: 16px 0;
        }
        .color-item {
            text-align: center;
            padding: 12px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            min-width: 120px;
        }
        .primary { background: linear-gradient(135deg, #3498db, #2980b9); }
        .secondary { background: linear-gradient(135deg, #2c3e50, #34495e); }
        .accent { background: linear-gradient(135deg, #e74c3c, #c0392b); }
        .success { background: linear-gradient(135deg, #2ecc71, #27ae60); }
        .warning { background: linear-gradient(135deg, #f39c12, #e67e22); }

        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
            position: relative;
            padding-left: 24px;
        }
        .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #2ecc71;
            font-weight: bold;
        }

        .demo-menu {
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        .demo-menu-item {
            color: #bdc3c7;
            padding: 12px 16px;
            margin: 4px 0;
            border-radius: 6px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .demo-menu-item:hover {
            background: rgba(52, 152, 219, 0.2);
            color: #3498db;
            transform: translateX(4px);
        }
        .demo-menu-item.active {
            background: rgba(52, 152, 219, 0.15);
            color: #3498db;
            border-left: 4px solid #3498db;
        }
    </style>
</head>
<body class="demo-container">
    <div class="demo-card">
        <h1 class="demo-title">✨ 精致菜单界面优化方案</h1>
        <p style="color: #7f8c8d; font-size: 16px; line-height: 1.6;">
            为面向客户的OWMS-OV系统设计的精致、简洁菜单界面配色方案，注重细节和用户体验。
        </p>
    </div>

    <div class="demo-card">
        <h2 class="demo-title">🎯 优化特性</h2>
        <ul class="feature-list">
            <li>精致的深色主题，专业而不失温和</li>
            <li>简洁的菜单项交互，突出重点信息</li>
            <li>统一的绿色系配色，清新且现代</li>
            <li>优化的字体和间距，提升可读性</li>
            <li>微妙的阴影效果，增强层次感</li>
            <li>平滑的过渡动画，提升操作体验</li>
            <li>精心调整的图标透明度</li>
            <li>保持LayUI原有的稳定性和兼容性</li>
        </ul>
    </div>

    <div class="demo-card">
        <h2 class="demo-title">🎨 配色方案</h2>
        <div class="color-palette">
            <div class="color-item" style="background: #2c3e50;">
                <div>顶部导航</div>
                <small>#2c3e50</small>
            </div>
            <div class="color-item" style="background: #34495e;">
                <div>侧边栏</div>
                <small>#34495e</small>
            </div>
            <div class="color-item" style="background: #1abc9c;">
                <div>主色调</div>
                <small>#1abc9c</small>
            </div>
            <div class="color-item" style="background: #3498db;">
                <div>蓝色强调</div>
                <small>#3498db</small>
            </div>
            <div class="color-item" style="background: #1AA094;">
                <div>绿色边框</div>
                <small>#1AA094</small>
            </div>
        </div>
    </div>

    <div class="demo-card">
        <h2 class="demo-title">📱 菜单效果演示</h2>
        <div class="demo-menu">
            <div class="demo-menu-item active">
                <i class="layui-icon layui-icon-home"></i> 首页仪表板
            </div>
            <div class="demo-menu-item">
                <i class="layui-icon layui-icon-user"></i> 用户管理
            </div>
            <div class="demo-menu-item">
                <i class="layui-icon layui-icon-set"></i> 系统设置
            </div>
            <div class="demo-menu-item">
                <i class="layui-icon layui-icon-chart"></i> 数据分析
            </div>
            <div class="demo-menu-item">
                <i class="layui-icon layui-icon-file"></i> 文档管理
            </div>
        </div>
        <p style="color: #7f8c8d; font-size: 14px; margin-top: 12px;">
            ↑ 鼠标悬停查看交互效果
        </p>
    </div>

    <div class="demo-card">
        <h2 class="demo-title">🚀 技术实现</h2>
        <div style="background: #f8f9fa; padding: 16px; border-radius: 6px; border-left: 4px solid #3498db;">
            <h4 style="color: #2c3e50; margin-top: 0;">主要改进内容：</h4>
            <ul style="color: #7f8c8d; line-height: 1.6;">
                <li><strong>CSS变量系统：</strong> 使用CSS自定义属性统一管理颜色和样式</li>
                <li><strong>渐变背景：</strong> 顶部导航和侧边栏采用现代化渐变效果</li>
                <li><strong>微交互动画：</strong> 添加hover、focus和active状态的平滑过渡</li>
                <li><strong>阴影系统：</strong> 分层阴影设计增强界面层次感</li>
                <li><strong>响应式优化：</strong> 针对移动端和平板设备的适配</li>
            </ul>
        </div>
    </div>

    <div class="demo-card">
        <h2 class="demo-title">📋 文件清单</h2>
        <div style="background: #2c3e50; color: #ecf0f1; padding: 16px; border-radius: 6px; font-family: 'Courier New', monospace;">
            <div style="color: #3498db;">已优化的文件：</div>
            <div>├── wwwroot/css/main.css (主样式文件优化)</div>
            <div>├── wwwroot/css/modern-theme.css (新增现代化主题)</div>
            <div>└── Views/Home/Index.cshtml (引入新样式文件)</div>
        </div>
    </div>

    <script>
        // 简单的交互演示
        document.querySelectorAll('.demo-menu-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.demo-menu-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>
